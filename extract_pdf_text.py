import PyPDF2
from pathlib import Path

pdf_path = Path('test_cover_letter.pdf')
output_path = Path('test_cover_letter_text.txt')

with open(pdf_path, 'rb') as f:
    reader = PyPDF2.PdfReader(f)
    all_text = []
    for i, page in enumerate(reader.pages):
        text = page.extract_text()
        all_text.append(f'--- Page {i+1} ---\n{text}\n')

with open(output_path, 'w', encoding='utf-8') as out:
    out.writelines(all_text)

print(f'Extracted text saved to {output_path}')