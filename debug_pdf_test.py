#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试PDF生成测试
"""

import sys
import os
from pathlib import Path
import json
import traceback

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src" / "utils"))

def main():
    print("=== PDF生成调试测试 ===")
    
    try:
        # 检查HTML文件
        html_file = project_root / "cover-letter.html"
        if not html_file.exists():
            print(f"错误：找不到HTML文件 {html_file}")
            return
        
        print(f"找到HTML文件: {html_file}")
        
        # 导入模块
        print("导入转换模块...")
        from src.utils.html_to_json_converter import convert_html_to_json
        from src.utils.json_to_pdf_generator import generate_pdf_sync
        print("模块导入成功")
        
        # 转换HTML到JSON
        print("转换HTML到JSON...")
        config_dict = convert_html_to_json(str(html_file))
        print(f"转换成功，配置包含 {len(config_dict)} 个部分")
        
        # 保存配置用于调试
        debug_config_file = project_root / "debug-config.json"
        with open(debug_config_file, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, ensure_ascii=False, indent=2)
        print(f"调试配置已保存: {debug_config_file}")
        
        # 生成PDF
        print("生成PDF...")
        pdf_file = project_root / "debug-output.pdf"
        
        try:
            pdf_base64 = generate_pdf_sync(config_dict, str(pdf_file))
            
            if pdf_base64:
                print(f"PDF生成成功！")
                print(f"PDF文件: {pdf_file}")
                print(f"Base64长度: {len(pdf_base64)} 字符")
                
                # 检查文件是否存在
                if pdf_file.exists():
                    file_size = pdf_file.stat().st_size
                    print(f"PDF文件大小: {file_size} 字节")
                else:
                    print("警告：PDF文件未生成")
            else:
                print("错误：PDF生成返回空结果")
                
        except Exception as pdf_error:
            print(f"PDF生成错误: {pdf_error}")
            print("详细错误信息:")
            traceback.print_exc()
        
    except Exception as e:
        print(f"测试失败: {e}")
        print("详细错误信息:")
        traceback.print_exc()
    
    print("=== 调试测试完成 ===")

if __name__ == "__main__":
    main()