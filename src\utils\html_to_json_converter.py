#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML到JSON转换器
用于将cover-letter.html转换为JSON配置格式
"""

import re
import json
from typing import Dict, List, Optional, Any
from bs4 import BeautifulSoup
from pathlib import Path
from cover_letter_json_schema import (
    CoverLetterConfig, PageConfig, HeaderConfig, ContentBlock, 
    StatsSection, StatCard, SignatureConfig, FooterConfig,
    StyleConfig, FontConfig, GradientConfig, BorderConfig, 
    ShadowConfig, SpacingConfig, LogoConfig, FontWeight, TextAlign,
    DEFAULT_STYLES
)

class HTMLToJSONConverter:
    """HTML到JSON转换器"""
    
    def __init__(self):
        self.soup = None
        self.css_rules = {}
        
    def parse_html_file(self, html_file_path: str) -> CoverLetterConfig:
        """解析HTML文件并转换为JSON配置"""
        with open(html_file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        self.soup = BeautifulSoup(html_content, 'html.parser')
        
        # 解析CSS样式
        self._parse_css_styles()
        
        # 构建配置对象
        config = CoverLetterConfig(
            page=self._extract_page_config(),
            header=self._extract_header_config(),
            greeting=self._extract_greeting(),
            content_blocks=self._extract_content_blocks(),
            closing=self._extract_closing(),
            signature=self._extract_signature(),
            stats_section=self._extract_stats_section(),
            footer=self._extract_footer()
        )
        
        return config
    
    def _parse_css_styles(self):
        """解析CSS样式规则"""
        style_tags = self.soup.find_all('style')
        for style_tag in style_tags:
            css_content = style_tag.string
            if css_content:
                self._parse_css_content(css_content)
    
    def _parse_css_content(self, css_content: str):
        """解析CSS内容"""
        # 移除注释
        css_content = re.sub(r'/\*.*?\*/', '', css_content, flags=re.DOTALL)
        
        # 解析CSS规则
        rules = re.findall(r'([^{}]+)\s*{([^{}]*)}', css_content)
        for selector, properties in rules:
            selector = selector.strip()
            if selector:
                self.css_rules[selector] = self._parse_css_properties(properties)
    
    def _parse_css_properties(self, properties: str) -> Dict[str, str]:
        """解析CSS属性"""
        props = {}
        for prop in properties.split(';'):
            if ':' in prop:
                key, value = prop.split(':', 1)
                props[key.strip()] = value.strip()
        return props
    
    def _extract_page_config(self) -> PageConfig:
        """提取页面配置"""
        title_tag = self.soup.find('title')
        title = title_tag.text if title_tag else "求职信"
        
        # 提取字体导入
        fonts = []
        link_tags = self.soup.find_all('link', rel='stylesheet')
        for link in link_tags:
            href = link.get('href', '')
            if 'fonts.googleapis.com' in href:
                fonts.append(href)
        
        return PageConfig(
            title=title,
            language="zh-CN",
            fonts=fonts,
            body_style=DEFAULT_STYLES["page"]["body_style"],
            container_style=DEFAULT_STYLES["page"]["container_style"]
        )
    
    def _extract_header_config(self) -> HeaderConfig:
        """提取页眉配置"""
        header_elem = self.soup.find(class_='header')
        if not header_elem:
            raise ValueError("未找到header元素")
        
        # 提取标题和副标题
        title_elem = header_elem.find(class_='header-title')
        subtitle_elem = header_elem.find(class_='header-subtitle')
        
        title = title_elem.text.strip() if title_elem else "求职信"
        subtitle = subtitle_elem.text.strip() if subtitle_elem else ""
        
        # 从副标题中提取公司和职位信息
        company_name = ""
        position_title = ""
        if subtitle:
            # 假设格式为 "公司名称 - 职位名称"
            parts = subtitle.split(' - ')
            if len(parts) >= 2:
                company_name = parts[0].strip()
                position_title = parts[1].strip()
            else:
                company_name = subtitle
        
        # 提取Logo配置
        logo_config = None
        logo_elem = header_elem.find(class_='company-logo')
        if logo_elem:
            img_elem = logo_elem.find('img')
            if img_elem:
                logo_config = LogoConfig(
                    url=img_elem.get('src'),
                    placeholder_text=company_name,
                    width="60px",
                    height="60px"
                )
        
        return HeaderConfig(
            title=title,
            subtitle=subtitle,
            company_name=company_name,
            position_title=position_title,
            logo=logo_config,
            style=DEFAULT_STYLES["header"]
        )
    
    def _extract_greeting(self) -> str:
        """提取问候语"""
        greeting_elem = self.soup.find(class_='greeting')
        return greeting_elem.text.strip() if greeting_elem else "尊敬的招聘经理："
    
    def _extract_content_blocks(self) -> List[ContentBlock]:
        """提取内容块"""
        content_blocks = []
        content_elem = self.soup.find(class_='content')
        
        if not content_elem:
            return content_blocks
        
        # 查找主要内容区域
        main_content = content_elem.find(class_='main-content')
        if main_content:
            # 处理段落
            paragraphs = main_content.find_all('p')
            for p in paragraphs:
                text = p.get_text().strip()
                if text and not self._is_stats_content(text):
                    # 检测语言
                    language = self._detect_language(text)
                    
                    content_blocks.append(ContentBlock(
                        type="paragraph",
                        content=text,
                        language=language
                    ))
            
            # 处理分隔符
            separators = main_content.find_all(class_='separator')
            for sep in separators:
                content_blocks.append(ContentBlock(
                    type="separator",
                    content="---"
                ))
        
        return content_blocks
    
    def _extract_closing(self) -> str:
        """提取结尾语"""
        closing_elem = self.soup.find(class_='closing')
        return closing_elem.text.strip() if closing_elem else "此致\n敬礼！"
    
    def _extract_signature(self) -> SignatureConfig:
        """提取签名配置"""
        signature_elem = self.soup.find(class_='signature')
        
        name = "求职者"
        date = "2024年"
        
        if signature_elem:
            text = signature_elem.get_text()
            # 尝试解析姓名和日期
            lines = [line.strip() for line in text.split('\n') if line.strip()]
            if lines:
                name = lines[0]
                if len(lines) > 1:
                    date = lines[1]
        
        return SignatureConfig(
            name=name,
            date=date
        )
    
    def _extract_stats_section(self) -> Optional[StatsSection]:
        """提取统计分析区域"""
        stats_elem = self.soup.find(class_='stats-section')
        if not stats_elem:
            return None
        
        # 提取标题
        title_elem = stats_elem.find(class_='stats-title')
        title = title_elem.text.strip() if title_elem else "匹配度分析"
        
        # 提取统计卡片
        cards = []
        card_elems = stats_elem.find_all(class_='stat-card')
        
        for card_elem in card_elems:
            label_elem = card_elem.find(class_='stat-label')
            value_elem = card_elem.find(class_='stat-value')
            
            if label_elem and value_elem:
                label = label_elem.text.strip()
                value = value_elem.text.strip()
                
                # 尝试提取百分比
                percentage = None
                percentage_match = re.search(r'(\d+)%', value)
                if percentage_match:
                    percentage = int(percentage_match.group(1))
                
                cards.append(StatCard(
                    label=label,
                    value=value,
                    percentage=percentage
                ))
        
        return StatsSection(
            title=title,
            cards=cards,
            grid_columns=2,
            style=DEFAULT_STYLES["stats_section"]
        )
    
    def _extract_footer(self) -> Optional[FooterConfig]:
        """提取页脚配置"""
        footer_elem = self.soup.find(class_='footer')
        if footer_elem:
            return FooterConfig(
                content=footer_elem.text.strip()
            )
        return None
    
    def _is_stats_content(self, text: str) -> bool:
        """判断是否为统计内容"""
        stats_keywords = ['匹配度', '技能', '经验', '学习潜力', '文化契合', '%']
        return any(keyword in text for keyword in stats_keywords)
    
    def _detect_language(self, text: str) -> str:
        """检测文本语言"""
        # 简单的语言检测
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        total_chars = len(text)
        
        if chinese_chars > total_chars * 0.3:
            return "zh"
        else:
            return "en"

def convert_html_to_json(html_file_path: str, output_json_path: Optional[str] = None) -> Dict[str, Any]:
    """转换HTML文件为JSON配置"""
    converter = HTMLToJSONConverter()
    config = converter.parse_html_file(html_file_path)
    
    # 转换为字典
    config_dict = config.dict()
    
    # 保存到文件
    if output_json_path:
        with open(output_json_path, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, ensure_ascii=False, indent=2)
    
    return config_dict

if __name__ == "__main__":
    # 测试转换
    html_path = "d:/Jobs_Applier_AI_Agent_AIHawk-main/cover-letter.html"
    json_path = "d:/Jobs_Applier_AI_Agent_AIHawk-main/cover-letter-config.json"
    
    try:
        config = convert_html_to_json(html_path, json_path)
        print(f"转换成功！配置已保存到: {json_path}")
        print(f"配置包含 {len(config['content_blocks'])} 个内容块")
    except Exception as e:
        print(f"转换失败: {e}")