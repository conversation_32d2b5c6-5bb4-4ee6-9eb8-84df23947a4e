#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的求职信转换测试
"""

import os
import sys
import json
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
# 避免logging模块冲突，不添加src到路径
sys.path.insert(0, str(project_root / "src" / "utils"))

def main():
    print("简化测试开始...")
    
    # 检查HTML文件
    html_file = project_root / "cover-letter.html"
    if not html_file.exists():
        print(f"HTML文件不存在: {html_file}")
        return
    
    print(f"找到HTML文件: {html_file}")
    
    try:
        # 导入转换器
        from src.utils.html_to_json_converter import convert_html_to_json
        print("成功导入HTML到JSON转换器")
        
        # 转换HTML到JSON
        config_dict = convert_html_to_json(str(html_file))
        print(f"HTML转JSON成功，配置包含 {len(config_dict)} 个部分")
        
        # 保存JSON
        json_file = project_root / "test-config.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, ensure_ascii=False, indent=2)
        print(f"JSON配置已保存: {json_file}")
        
        # 测试PDF生成
        try:
            from src.utils.json_to_pdf_generator import JSONToPDFGenerator
            print("成功导入PDF生成器")

            generator = JSONToPDFGenerator()
            from src.utils.json_to_pdf_generator import generate_pdf_sync
            pdf_file = project_root / "test-output.pdf"
            pdf_base64 = generate_pdf_sync(config_dict, str(pdf_file))
            
            if pdf_base64:
                import base64
                pdf_file = project_root / "test-output.pdf"
                with open(pdf_file, 'wb') as f:
                    f.write(base64.b64decode(pdf_base64))
                print(f"PDF生成成功: {pdf_file}")
            else:
                print("PDF生成失败")
                
        except Exception as e:
            print(f"PDF生成错误: {e}")
        
        print("测试完成！")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()