#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的PDF生成测试 - 修复版本
测试正确的Pyppeteer API调用
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'webui', 'backend'))

async def test_pyppeteer_correct_api():
    """测试正确的Pyppeteer API调用"""
    try:
        from pyppeteer import launch
        
        print("启动浏览器...")
        browser = await launch({
            'headless': True,
            'args': ['--no-sandbox', '--disable-setuid-sandbox']
        })
        
        print("创建页面...")
        page = await browser.newPage()
        
        # 测试HTML内容
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>PDF生成测试</title>
            <style>
                body { 
                    font-family: Arial, sans-serif; 
                    padding: 20px; 
                    line-height: 1.6;
                }
                h1 { 
                    color: #333; 
                    border-bottom: 2px solid #007acc;
                    padding-bottom: 10px;
                }
                .success { 
                    color: #28a745; 
                    font-weight: bold;
                }
            </style>
        </head>
        <body>
            <h1>PDF生成API修复测试</h1>
            <p class="success">✓ Pyppeteer API调用修复成功！</p>
            <p>这个PDF是使用修复后的API生成的：</p>
            <ul>
                <li>✓ 移除了不支持的waitUntil参数</li>
                <li>✓ 使用正确的setContent()调用</li>
                <li>✓ 分别处理页面加载等待</li>
                <li>✓ 使用waitForTimeout()替代过时的waitFor()</li>
            </ul>
            <p><strong>修复时间：</strong> 2025-01-24</p>
            <p><strong>状态：</strong> <span class="success">修复完成</span></p>
        </body>
        </html>
        """
        
        print("设置页面内容（使用修复后的API）...")
        # 使用修复后的API - 只传递HTML内容
        await page.setContent(html_content)
        
        print("等待页面加载完成...")
        await page.waitForSelector('body')
        
        # 使用asyncio.sleep等待渲染完成
        print("等待渲染完成...")
        await asyncio.sleep(2)
        
        print("生成PDF...")
        pdf_buffer = await page.pdf({
            'format': 'A4',
            'printBackground': True,
            'margin': {
                'top': '20mm',
                'right': '15mm',
                'bottom': '20mm',
                'left': '15mm'
            }
        })
        
        await browser.close()
        
        # 保存PDF
        output_file = 'pyppeteer_api_fixed.pdf'
        with open(output_file, 'wb') as f:
            f.write(pdf_buffer)
        
        print(f"✓ PDF生成成功！文件已保存为 {output_file}")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("Pyppeteer API修复验证测试")
    print("=" * 60)
    
    try:
        result = asyncio.run(test_pyppeteer_correct_api())
        
        if result:
            print("\n🎉 Pyppeteer API修复验证成功！")
            print("\n修复内容总结:")
            print("1. ✓ 移除了setContent()的不支持参数")
            print("2. ✓ 使用正确的API调用方式")
            print("3. ✓ 分离了内容设置和加载等待")
            print("4. ✓ 更新了过时的方法调用")
            print("\n现在可以正常生成PDF了！")
        else:
            print("\n❌ 测试失败，请检查错误信息")
            
    except Exception as e:
        print(f"\n❌ 运行测试时出错: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)