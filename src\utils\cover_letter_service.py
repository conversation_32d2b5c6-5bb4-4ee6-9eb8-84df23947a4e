#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
求职信服务
整合HTML→JSON→PDF的完整转换流程
"""

import json
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import logging
from cover_letter_json_schema import CoverLetterConfig, DEFAULT_STYLES
from html_to_json_converter import H<PERSON>LToJ<PERSON>NConverter, convert_html_to_json
from json_to_pdf_generator import JSONToPDFGenerator, generate_pdf_sync

logger = logging.getLogger(__name__)

class CoverLetterService:
    """求职信服务类"""
    
    def __init__(self):
        self.html_converter = HTMLToJSONConverter()
        self.pdf_generator = JSONToPDFGenerator()
        self.config_cache = {}
    
    def convert_html_to_config(self, html_file_path: str, cache_key: Optional[str] = None) -> Dict[str, Any]:
        """将HTML文件转换为JSON配置"""
        try:
            # 检查缓存
            if cache_key and cache_key in self.config_cache:
                logger.info(f"从缓存获取配置: {cache_key}")
                return self.config_cache[cache_key]
            
            # 转换HTML到JSON
            logger.info(f"开始转换HTML文件: {html_file_path}")
            config_dict = convert_html_to_json(html_file_path)
            
            # 缓存配置
            if cache_key:
                self.config_cache[cache_key] = config_dict
                logger.info(f"配置已缓存: {cache_key}")
            
            return config_dict
            
        except Exception as e:
            logger.error(f"HTML转换失败: {e}")
            raise
    
    def generate_pdf_from_config(self, config_dict: Dict[str, Any], output_path: Optional[str] = None) -> str:
        """从JSON配置生成PDF"""
        try:
            logger.info("开始从JSON配置生成PDF")
            
            # 验证配置
            config = CoverLetterConfig(**config_dict)
            
            # 生成PDF
            pdf_base64 = generate_pdf_sync(config_dict, output_path)
            
            logger.info(f"PDF生成成功，大小: {len(pdf_base64)} 字符")
            return pdf_base64
            
        except Exception as e:
            logger.error(f"PDF生成失败: {e}")
            raise
    
    def convert_html_to_pdf(self, html_file_path: str, output_path: Optional[str] = None, 
                           cache_key: Optional[str] = None) -> str:
        """完整的HTML到PDF转换流程"""
        try:
            # 第一步：HTML → JSON
            config_dict = self.convert_html_to_config(html_file_path, cache_key)
            
            # 第二步：JSON → PDF
            pdf_base64 = self.generate_pdf_from_config(config_dict, output_path)
            
            return pdf_base64
            
        except Exception as e:
            logger.error(f"完整转换流程失败: {e}")
            raise
    
    def update_config_content(self, config_dict: Dict[str, Any], 
                            new_content: Dict[str, Any]) -> Dict[str, Any]:
        """更新配置中的内容（保持样式不变）"""
        try:
            updated_config = config_dict.copy()
            
            # 更新页眉信息
            if 'header' in new_content:
                header_updates = new_content['header']
                for key, value in header_updates.items():
                    if key in updated_config['header']:
                        updated_config['header'][key] = value
            
            # 更新问候语
            if 'greeting' in new_content:
                updated_config['greeting'] = new_content['greeting']
            
            # 更新内容块
            if 'content_blocks' in new_content:
                # 保持原有样式，只更新文本内容
                new_blocks = new_content['content_blocks']
                if len(new_blocks) <= len(updated_config['content_blocks']):
                    for i, new_block in enumerate(new_blocks):
                        if 'content' in new_block:
                            updated_config['content_blocks'][i]['content'] = new_block['content']
                        if 'language' in new_block:
                            updated_config['content_blocks'][i]['language'] = new_block['language']
            
            # 更新结尾语
            if 'closing' in new_content:
                updated_config['closing'] = new_content['closing']
            
            # 更新签名
            if 'signature' in new_content:
                signature_updates = new_content['signature']
                for key, value in signature_updates.items():
                    if key in updated_config['signature']:
                        updated_config['signature'][key] = value
            
            # 更新统计数据
            if 'stats_section' in new_content and updated_config.get('stats_section'):
                stats_updates = new_content['stats_section']
                
                # 更新标题
                if 'title' in stats_updates:
                    updated_config['stats_section']['title'] = stats_updates['title']
                
                # 更新卡片数据
                if 'cards' in stats_updates:
                    new_cards = stats_updates['cards']
                    existing_cards = updated_config['stats_section']['cards']
                    
                    for i, new_card in enumerate(new_cards):
                        if i < len(existing_cards):
                            for key, value in new_card.items():
                                if key in existing_cards[i]:
                                    existing_cards[i][key] = value
            
            return updated_config
            
        except Exception as e:
            logger.error(f"配置更新失败: {e}")
            raise
    
    def generate_custom_pdf(self, base_html_path: str, content_updates: Dict[str, Any], 
                          output_path: Optional[str] = None) -> str:
        """基于基础HTML模板生成自定义PDF"""
        try:
            # 获取基础配置
            base_config = self.convert_html_to_config(base_html_path, "base_template")
            
            # 更新内容
            updated_config = self.update_config_content(base_config, content_updates)
            
            # 生成PDF
            pdf_base64 = self.generate_pdf_from_config(updated_config, output_path)
            
            return pdf_base64
            
        except Exception as e:
            logger.error(f"自定义PDF生成失败: {e}")
            raise
    
    def validate_config(self, config_dict: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """验证配置的有效性"""
        errors = []
        
        try:
            # 使用Pydantic验证
            CoverLetterConfig(**config_dict)
            return True, []
            
        except Exception as e:
            errors.append(str(e))
            return False, errors
    
    def get_config_template(self) -> Dict[str, Any]:
        """获取配置模板"""
        return {
            "page": {
                "title": "求职信",
                "language": "zh-CN",
                "charset": "UTF-8",
                "viewport": "width=device-width, initial-scale=1.0",
                "fonts": [
                    "https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap"
                ],
                "body_style": None,
                "container_style": None
            },
            "header": {
                "title": "求职信",
                "subtitle": "公司名称 - 职位名称",
                "company_name": "公司名称",
                "position_title": "职位名称",
                "job_url": None,
                "location": None,
                "logo": None,
                "style": None
            },
            "greeting": "尊敬的招聘经理：",
            "content_blocks": [
                {
                    "type": "paragraph",
                    "content": "我对贵公司的职位非常感兴趣...",
                    "language": "zh",
                    "style": None,
                    "custom_class": None
                }
            ],
            "closing": "此致\n敬礼！",
            "signature": {
                "name": "求职者姓名",
                "date": "2024年",
                "style": None
            },
            "stats_section": None,
            "footer": None
        }
    
    def clear_cache(self):
        """清除配置缓存"""
        self.config_cache.clear()
        logger.info("配置缓存已清除")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        return {
            "cache_size": len(self.config_cache),
            "cached_keys": list(self.config_cache.keys())
        }

# 全局服务实例
cover_letter_service = CoverLetterService()

# 便捷函数
def convert_cover_letter_html_to_pdf(html_file_path: str, output_path: Optional[str] = None) -> str:
    """便捷函数：将求职信HTML转换为PDF"""
    return cover_letter_service.convert_html_to_pdf(html_file_path, output_path)

def generate_custom_cover_letter_pdf(content_updates: Dict[str, Any], 
                                   base_template_path: Optional[str] = None,
                                   output_path: Optional[str] = None) -> str:
    """便捷函数：生成自定义求职信PDF"""
    if not base_template_path:
        base_template_path = "d:/Jobs_Applier_AI_Agent_AIHawk-main/cover-letter.html"
    
    return cover_letter_service.generate_custom_pdf(
        base_template_path, content_updates, output_path
    )

def validate_cover_letter_config(config_dict: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """便捷函数：验证求职信配置"""
    return cover_letter_service.validate_config(config_dict)

if __name__ == "__main__":
    # 测试转换流程
    html_path = "d:/Jobs_Applier_AI_Agent_AIHawk-main/cover-letter.html"
    output_path = "d:/Jobs_Applier_AI_Agent_AIHawk-main/test-output.pdf"
    
    try:
        # 测试完整转换流程
        print("开始测试HTML→JSON→PDF转换流程...")
        pdf_base64 = convert_cover_letter_html_to_pdf(html_path, output_path)
        print(f"转换成功！PDF已保存到: {output_path}")
        print(f"Base64长度: {len(pdf_base64)} 字符")
        
        # 测试自定义内容生成
        print("\n开始测试自定义内容生成...")
        custom_content = {
            "header": {
                "company_name": "测试公司",
                "position_title": "测试职位"
            },
            "greeting": "尊敬的HR：",
            "content_blocks": [
                {
                    "content": "这是一段测试内容，用于验证自定义生成功能。",
                    "language": "zh"
                }
            ],
            "signature": {
                "name": "测试用户",
                "date": "2024年12月"
            }
        }
        
        custom_pdf = generate_custom_cover_letter_pdf(
            custom_content, 
            html_path, 
            "d:/Jobs_Applier_AI_Agent_AIHawk-main/test-custom.pdf"
        )
        print("自定义PDF生成成功！")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()