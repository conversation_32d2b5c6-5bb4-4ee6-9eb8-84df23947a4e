#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
求职信JSON配置格式定义
用于HTML→JSON→PDF的转换流程
"""

from typing import Dict, List, Optional, Union
from pydantic import BaseModel, Field
from enum import Enum

class FontWeight(str, Enum):
    """字体粗细枚举"""
    LIGHT = "300"
    NORMAL = "400"
    MEDIUM = "500"
    SEMIBOLD = "600"
    BOLD = "700"
    EXTRABOLD = "800"

class TextAlign(str, Enum):
    """文本对齐方式"""
    LEFT = "left"
    CENTER = "center"
    RIGHT = "right"
    JUSTIFY = "justify"

class GradientConfig(BaseModel):
    """渐变配置"""
    type: str = Field(default="linear", description="渐变类型: linear, radial")
    direction: str = Field(default="135deg", description="渐变方向")
    colors: List[str] = Field(description="渐变颜色列表")
    stops: Optional[List[str]] = Field(default=None, description="渐变停止点")

class BorderConfig(BaseModel):
    """边框配置"""
    width: str = Field(default="1px", description="边框宽度")
    style: str = Field(default="solid", description="边框样式")
    color: str = Field(default="#e2e8f0", description="边框颜色")
    radius: str = Field(default="0px", description="圆角半径")

class ShadowConfig(BaseModel):
    """阴影配置"""
    x: str = Field(default="0px", description="水平偏移")
    y: str = Field(default="2px", description="垂直偏移")
    blur: str = Field(default="4px", description="模糊半径")
    spread: str = Field(default="0px", description="扩散半径")
    color: str = Field(default="rgba(0,0,0,0.1)", description="阴影颜色")

class SpacingConfig(BaseModel):
    """间距配置"""
    top: str = Field(default="0px")
    right: str = Field(default="0px")
    bottom: str = Field(default="0px")
    left: str = Field(default="0px")

class FontConfig(BaseModel):
    """字体配置"""
    family: str = Field(default="'Inter', 'Microsoft YaHei', sans-serif", description="字体族")
    size: str = Field(default="16px", description="字体大小")
    weight: FontWeight = Field(default=FontWeight.NORMAL, description="字体粗细")
    line_height: str = Field(default="1.6", description="行高")
    color: str = Field(default="#2d3748", description="字体颜色")
    letter_spacing: str = Field(default="normal", description="字母间距")

class StyleConfig(BaseModel):
    """通用样式配置"""
    background: Optional[str] = Field(default=None, description="背景色")
    background_gradient: Optional[GradientConfig] = Field(default=None, description="背景渐变")
    padding: Optional[SpacingConfig] = Field(default=None, description="内边距")
    margin: Optional[SpacingConfig] = Field(default=None, description="外边距")
    border: Optional[BorderConfig] = Field(default=None, description="边框")
    shadow: Optional[ShadowConfig] = Field(default=None, description="阴影")
    font: Optional[FontConfig] = Field(default=None, description="字体")
    text_align: Optional[TextAlign] = Field(default=None, description="文本对齐")
    width: Optional[str] = Field(default=None, description="宽度")
    height: Optional[str] = Field(default=None, description="高度")
    display: Optional[str] = Field(default=None, description="显示方式")
    position: Optional[str] = Field(default=None, description="定位方式")
    z_index: Optional[int] = Field(default=None, description="层级")
    opacity: Optional[float] = Field(default=None, description="透明度")
    transform: Optional[str] = Field(default=None, description="变换")
    transition: Optional[str] = Field(default=None, description="过渡效果")
    overflow: Optional[str] = Field(default=None, description="溢出处理")
    custom_css: Optional[Dict[str, str]] = Field(default=None, description="自定义CSS属性")

class LogoConfig(BaseModel):
    """Logo配置"""
    url: Optional[str] = Field(default=None, description="Logo URL")
    fallback_urls: Optional[List[str]] = Field(default=None, description="备用Logo URLs")
    placeholder_text: str = Field(description="占位符文字")
    width: str = Field(default="60px", description="Logo宽度")
    height: str = Field(default="60px", description="Logo高度")
    style: Optional[StyleConfig] = Field(default=None, description="Logo样式")

class HeaderConfig(BaseModel):
    """页眉配置"""
    title: str = Field(description="标题")
    subtitle: str = Field(description="副标题")
    company_name: str = Field(description="公司名称")
    position_title: str = Field(description="职位名称")
    job_url: Optional[str] = Field(default=None, description="职位链接")
    location: Optional[str] = Field(default=None, description="工作地点")
    logo: Optional[LogoConfig] = Field(default=None, description="Logo配置")
    style: Optional[StyleConfig] = Field(default=None, description="页眉样式")

class ContentBlock(BaseModel):
    """内容块"""
    type: str = Field(description="内容类型: text, paragraph, list, separator")
    content: str = Field(description="内容文本")
    language: Optional[str] = Field(default=None, description="语言标识: zh, en")
    style: Optional[StyleConfig] = Field(default=None, description="内容样式")
    custom_class: Optional[str] = Field(default=None, description="自定义CSS类名")

class StatCard(BaseModel):
    """统计卡片"""
    label: str = Field(description="标签")
    value: str = Field(description="数值")
    percentage: Optional[int] = Field(default=None, description="百分比值")
    show_progress_bar: bool = Field(default=True, description="是否显示进度条")
    style: Optional[StyleConfig] = Field(default=None, description="卡片样式")

class StatsSection(BaseModel):
    """统计分析区域"""
    title: str = Field(description="区域标题")
    cards: List[StatCard] = Field(description="统计卡片列表")
    grid_columns: int = Field(default=2, description="网格列数")
    style: Optional[StyleConfig] = Field(default=None, description="区域样式")

class SignatureConfig(BaseModel):
    """签名配置"""
    name: str = Field(description="签名姓名")
    date: str = Field(description="签名日期")
    style: Optional[StyleConfig] = Field(default=None, description="签名样式")

class FooterConfig(BaseModel):
    """页脚配置"""
    content: Optional[str] = Field(default=None, description="页脚内容")
    style: Optional[StyleConfig] = Field(default=None, description="页脚样式")

class PageConfig(BaseModel):
    """页面配置"""
    title: str = Field(description="页面标题")
    language: str = Field(default="zh-CN", description="页面语言")
    charset: str = Field(default="UTF-8", description="字符编码")
    viewport: str = Field(default="width=device-width, initial-scale=1.0", description="视口设置")
    fonts: List[str] = Field(default=[], description="字体导入列表")
    custom_css: Optional[str] = Field(default=None, description="自定义CSS")
    body_style: Optional[StyleConfig] = Field(default=None, description="页面主体样式")
    container_style: Optional[StyleConfig] = Field(default=None, description="容器样式")

class CoverLetterConfig(BaseModel):
    """求职信完整配置"""
    page: PageConfig = Field(description="页面配置")
    header: HeaderConfig = Field(description="页眉配置")
    greeting: str = Field(description="问候语")
    content_blocks: List[ContentBlock] = Field(description="内容块列表")
    closing: str = Field(description="结尾语")
    signature: SignatureConfig = Field(description="签名配置")
    stats_section: Optional[StatsSection] = Field(default=None, description="统计分析区域")
    footer: Optional[FooterConfig] = Field(default=None, description="页脚配置")
    
    class Config:
        """Pydantic配置"""
        use_enum_values = True
        validate_assignment = True
        extra = "forbid"

# 默认样式配置
DEFAULT_STYLES = {
    "page": {
        "body_style": StyleConfig(
            font=FontConfig(
                family="'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",
                size="16px",
                weight=FontWeight.NORMAL,
                line_height="1.7",
                color="#1a202c"
            ),
            background_gradient=GradientConfig(
                type="linear",
                direction="135deg",
                colors=["#667eea", "#764ba2", "#f093fb"]
            ),
            padding=SpacingConfig(top="30px", right="20px", bottom="30px", left="20px"),
            custom_css={"min-height": "100vh", "position": "relative"}
        ),
        "container_style": StyleConfig(
            background="rgba(255, 255, 255, 0.98)",
            border=BorderConfig(width="4px", style="solid", color="transparent", radius="24px"),
            shadow=ShadowConfig(x="0px", y="32px", blur="64px", color="rgba(0, 0, 0, 0.12)"),
            custom_css={
                "max-width": "900px",
                "margin": "0 auto",
                "backdrop-filter": "blur(20px)",
                "background-clip": "padding-box",
                "overflow": "hidden",
                "position": "relative"
            }
        )
    },
    "header": StyleConfig(
        background_gradient=GradientConfig(
            type="linear",
            direction="135deg",
            colors=["#667eea", "#764ba2"]
        ),
        padding=SpacingConfig(top="45px", right="40px", bottom="45px", left="40px"),
        font=FontConfig(color="white"),
        text_align=TextAlign.CENTER,
        custom_css={"position": "relative", "overflow": "hidden", "min-height": "180px"}
    ),
    "content": StyleConfig(
        padding=SpacingConfig(top="40px", right="50px", bottom="30px", left="50px"),
        background="white"
    ),
    "stats_section": StyleConfig(
        background_gradient=GradientConfig(
            type="linear",
            direction="135deg",
            colors=["#f7fafc", "#edf2f7"]
        ),
        border=BorderConfig(width="1px", style="solid", color="#e2e8f0", radius="20px"),
        padding=SpacingConfig(top="25px", right="25px", bottom="25px", left="25px"),
        margin=SpacingConfig(top="25px", bottom="15px"),
        custom_css={"position": "relative", "overflow": "hidden"}
    )
}