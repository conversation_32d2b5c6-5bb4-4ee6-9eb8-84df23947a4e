#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSON到PDF生成器
根据JSON配置生成PDF求职信
"""

import asyncio
import base64
from typing import Dict, List, Optional, Any
from pathlib import Path
from pyppeteer import launch
from cover_letter_json_schema import (
    CoverLetterConfig, StyleConfig, GradientConfig, 
    BorderConfig, ShadowConfig, SpacingConfig, FontConfig
)

class JSONToPDFGenerator:
    """JSON到PDF生成器"""
    
    def __init__(self):
        self.browser = None
        self.page = None
    
    async def generate_pdf_from_json(self, config: CoverLetterConfig, output_path: Optional[str] = None) -> str:
        """从JSON配置生成PDF"""
        try:
            # 生成HTML
            html_content = self._generate_html_from_config(config)
            
            # 启动浏览器
            self.browser = await launch({
                'headless': True,
                'args': [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                    '--no-first-run',
                    '--no-default-browser-check',
                    '--disable-default-apps'
                ]
            })
            
            self.page = await self.browser.newPage()
            
            # 设置视口为A4比例
            await self.page.setViewport({'width': 794, 'height': 1123})
            
            # 加载HTML内容
            await self.page.setContent(html_content)
            
            # 等待渲染完成
            await asyncio.sleep(3)
            
            # 生成PDF
            pdf_buffer = await self.page.pdf({
                'format': 'A4',
                'printBackground': True,
                'margin': {
                    'top': '0.5in',
                    'right': '0.5in',
                    'bottom': '0.5in',
                    'left': '0.5in'
                },
                'preferCSSPageSize': True
            })
            
            # 保存到文件
            if output_path:
                with open(output_path, 'wb') as f:
                    f.write(pdf_buffer)
            
            # 返回base64编码
            return base64.b64encode(pdf_buffer).decode('utf-8')
            
        finally:
            if self.browser:
                await self.browser.close()
    
    def _generate_html_from_config(self, config: CoverLetterConfig) -> str:
        """从配置生成HTML"""
        html_parts = []
        
        # HTML头部
        html_parts.append(self._generate_html_head(config))
        
        # 页面主体开始
        body_style = self._style_to_css(config.page.body_style) if config.page.body_style else ""
        html_parts.append(f'<body style="{body_style}">')
        
        # 容器开始
        container_style = self._style_to_css(config.page.container_style) if config.page.container_style else ""
        html_parts.append(f'<div class="container" style="{container_style}">')
        
        # 渐变边框伪元素（如果需要）
        if config.page.container_style and config.page.container_style.custom_css:
            html_parts.append('<div class="container-gradient"></div>')
        
        # 页眉
        html_parts.append(self._generate_header(config.header))
        
        # 内容区域
        html_parts.append(self._generate_content(config))
        
        # 统计分析区域
        if config.stats_section:
            html_parts.append(self._generate_stats_section(config.stats_section))
        
        # 页脚
        if config.footer:
            html_parts.append(self._generate_footer(config.footer))
        
        # 容器结束
        html_parts.append('</div>')
        
        # 页面主体结束
        html_parts.append('</body></html>')
        
        return '\n'.join(html_parts)
    
    def _generate_html_head(self, config: CoverLetterConfig) -> str:
        """生成HTML头部"""
        head_parts = [
            '<!DOCTYPE html>',
            f'<html lang="{config.page.language}">',
            '<head>',
            f'<meta charset="{config.page.charset}">',
            f'<meta name="viewport" content="{config.page.viewport}">',
            f'<title>{config.page.title}</title>'
        ]
        
        # 字体导入
        for font_url in config.page.fonts:
            head_parts.append(f'<link rel="stylesheet" href="{font_url}">')
        
        # CSS样式
        head_parts.append('<style>')
        head_parts.append(self._generate_css_styles(config))
        head_parts.append('</style>')
        
        # 自定义CSS
        if config.page.custom_css:
            head_parts.append(f'<style>{config.page.custom_css}</style>')
        
        head_parts.append('</head>')
        
        return '\n'.join(head_parts)
    
    def _generate_css_styles(self, config: CoverLetterConfig) -> str:
        """生成CSS样式"""
        css_parts = []
        
        # 基础样式重置
        css_parts.append("""
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        """)
        
        # 容器渐变边框样式
        if config.page.container_style:
            css_parts.append("""
        .container {
            position: relative;
        }
        
        .container::before {
            content: '';
            position: absolute;
            top: -4px;
            left: -4px;
            right: -4px;
            bottom: -4px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            border-radius: 28px;
            z-index: -1;
            animation: gradientShift 8s ease-in-out infinite;
        }
        
        @keyframes gradientShift {
            0%, 100% { background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%); }
            25% { background: linear-gradient(135deg, #f093fb 0%, #667eea 50%, #764ba2 100%); }
            50% { background: linear-gradient(135deg, #764ba2 0%, #f093fb 50%, #667eea 100%); }
            75% { background: linear-gradient(135deg, #667eea 0%, #f093fb 50%, #764ba2 100%); }
        }
        """)
        
        # 页眉样式
        if config.header.style:
            header_css = self._style_to_css(config.header.style)
            css_parts.append(f'.header {{ {header_css} }}')
        
        # 统计区域样式
        if config.stats_section and config.stats_section.style:
            stats_css = self._style_to_css(config.stats_section.style)
            css_parts.append(f'.stats-section {{ {stats_css} }}')
            
            # 统计卡片网格
            grid_columns = config.stats_section.grid_columns
            css_parts.append(f"""
            .stats-grid {{
                display: grid;
                grid-template-columns: repeat({grid_columns}, 1fr);
                gap: 20px;
                margin-top: 20px;
            }}
            
            .stat-card {{
                background: white;
                border: 1px solid #e2e8f0;
                border-radius: 12px;
                padding: 20px;
                text-align: center;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                transition: transform 0.2s ease;
            }}
            
            .stat-card:hover {{
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            }}
            
            .stat-value {{
                font-size: 2em;
                font-weight: 700;
                color: #667eea;
                margin-bottom: 8px;
            }}
            
            .stat-label {{
                font-size: 0.9em;
                color: #64748b;
                font-weight: 500;
            }}
            
            .match-bar {{
                width: 100%;
                height: 8px;
                background: #e2e8f0;
                border-radius: 4px;
                margin-top: 10px;
                overflow: hidden;
            }}
            
            .match-bar-fill {{
                height: 100%;
                background: linear-gradient(90deg, #667eea, #764ba2);
                border-radius: 4px;
                transition: width 1s ease;
            }}
            """)
        
        # 响应式样式
        css_parts.append("""
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 16px;
            }
            
            .header {
                padding: 30px 20px;
            }
            
            .content {
                padding: 30px 25px;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }
        
        @media print {
            @page {
                margin: 0.5in;
                size: A4;
            }
            
            body {
                background: white !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            
            .container {
                box-shadow: none !important;
                border: 1px solid #e2e8f0 !important;
                background: white !important;
            }
            
            .container::before {
                display: none !important;
            }
            
            .header {
                background: linear-gradient(135deg, #667eea, #764ba2) !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            
            .stats-section {
                background: #f7fafc !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
        }
        """)
        
        return '\n'.join(css_parts)
    
    def _generate_header(self, header_config) -> str:
        """生成页眉HTML"""
        header_parts = ['<div class="header">']
        
        # Logo
        if header_config.logo:
            logo_style = self._style_to_css(header_config.logo.style) if header_config.logo.style else ""
            header_parts.append(f'<div class="company-logo" style="{logo_style}">')
            
            if header_config.logo.url:
                header_parts.append(
                    f'<img src="{header_config.logo.url}" '
                    f'alt="{header_config.logo.placeholder_text}" '
                    f'style="width: {header_config.logo.width}; height: {header_config.logo.height}; object-fit: contain;">')
            else:
                header_parts.append(
                    f'<div style="width: {header_config.logo.width}; height: {header_config.logo.height}; '
                    f'background: rgba(255,255,255,0.2); border-radius: 12px; display: flex; '
                    f'align-items: center; justify-content: center; color: white; font-weight: 600;">')
                header_parts.append(header_config.logo.placeholder_text[:2].upper())
                header_parts.append('</div>')
            
            header_parts.append('</div>')
        
        # 标题和副标题
        header_parts.append(f'<h1 class="header-title">{header_config.title}</h1>')
        if header_config.subtitle:
            header_parts.append(f'<p class="header-subtitle">{header_config.subtitle}</p>')
        
        # 职位信息
        if header_config.position_title:
            header_parts.append(f'<p class="position-info">{header_config.position_title}</p>')
        
        if header_config.location:
            header_parts.append(f'<p class="location-info">{header_config.location}</p>')
        
        header_parts.append('</div>')
        
        return '\n'.join(header_parts)
    
    def _generate_content(self, config: CoverLetterConfig) -> str:
        """生成内容区域HTML"""
        content_parts = ['<div class="content">']
        
        # 问候语
        content_parts.append(f'<p class="greeting">{config.greeting}</p>')
        
        # 内容块
        content_parts.append('<div class="main-content">')
        
        for block in config.content_blocks:
            if block.type == "paragraph":
                class_attr = f' class="{block.custom_class}"' if block.custom_class else ''
                lang_attr = f' lang="{block.language}"' if block.language else ''
                style_attr = f' style="{self._style_to_css(block.style)}"' if block.style else ''
                
                content_parts.append(
                    f'<p{class_attr}{lang_attr}{style_attr}>{block.content}</p>'
                )
            
            elif block.type == "separator":
                content_parts.append('<div class="separator"><hr></div>')
            
            elif block.type == "list":
                # 处理列表内容
                items = block.content.split('\n')
                content_parts.append('<ul>')
                for item in items:
                    if item.strip():
                        content_parts.append(f'<li>{item.strip()}</li>')
                content_parts.append('</ul>')
        
        content_parts.append('</div>')
        
        # 结尾语
        content_parts.append(f'<p class="closing">{config.closing}</p>')
        
        # 签名
        content_parts.append('<div class="signature">')
        content_parts.append(f'<p>{config.signature.name}</p>')
        content_parts.append(f'<p>{config.signature.date}</p>')
        content_parts.append('</div>')
        
        content_parts.append('</div>')
        
        return '\n'.join(content_parts)
    
    def _generate_stats_section(self, stats_config) -> str:
        """生成统计分析区域HTML"""
        stats_parts = ['<div class="stats-section">']
        
        # 标题
        stats_parts.append(f'<h3 class="stats-title">{stats_config.title}</h3>')
        
        # 统计卡片网格
        stats_parts.append('<div class="stats-grid">')
        
        for card in stats_config.cards:
            stats_parts.append('<div class="stat-card">')
            stats_parts.append(f'<div class="stat-value">{card.value}</div>')
            stats_parts.append(f'<div class="stat-label">{card.label}</div>')
            
            # 进度条
            if card.show_progress_bar and card.percentage is not None:
                stats_parts.append('<div class="match-bar">')
                stats_parts.append(
                    f'<div class="match-bar-fill" style="width: {card.percentage}%;"></div>'
                )
                stats_parts.append('</div>')
            
            stats_parts.append('</div>')
        
        stats_parts.append('</div>')
        stats_parts.append('</div>')
        
        return '\n'.join(stats_parts)
    
    def _generate_footer(self, footer_config) -> str:
        """生成页脚HTML"""
        if footer_config.content:
            style_attr = f' style="{self._style_to_css(footer_config.style)}"' if footer_config.style else ''
            return f'<div class="footer"{style_attr}>{footer_config.content}</div>'
        return ''
    
    def _style_to_css(self, style: StyleConfig) -> str:
        """将样式配置转换为CSS字符串"""
        if not style:
            return ""
        
        css_parts = []
        
        # 基础属性
        if style.background:
            css_parts.append(f"background: {style.background}")
        
        if style.width:
            css_parts.append(f"width: {style.width}")
        
        if style.height:
            css_parts.append(f"height: {style.height}")
        
        if style.display:
            css_parts.append(f"display: {style.display}")
        
        if style.position:
            css_parts.append(f"position: {style.position}")
        
        if style.z_index is not None:
            css_parts.append(f"z-index: {style.z_index}")
        
        if style.opacity is not None:
            css_parts.append(f"opacity: {style.opacity}")
        
        if style.transform:
            css_parts.append(f"transform: {style.transform}")
        
        if style.transition:
            css_parts.append(f"transition: {style.transition}")
        
        if style.overflow:
            css_parts.append(f"overflow: {style.overflow}")
        
        if style.text_align:
            css_parts.append(f"text-align: {style.text_align}")
        
        # 背景渐变
        if style.background_gradient:
            gradient = self._gradient_to_css(style.background_gradient)
            css_parts.append(f"background: {gradient}")
        
        # 字体
        if style.font:
            font_css = self._font_to_css(style.font)
            css_parts.extend(font_css)
        
        # 边距
        if style.padding:
            padding_css = self._spacing_to_css(style.padding, "padding")
            css_parts.append(padding_css)
        
        if style.margin:
            margin_css = self._spacing_to_css(style.margin, "margin")
            css_parts.append(margin_css)
        
        # 边框
        if style.border:
            border_css = self._border_to_css(style.border)
            css_parts.extend(border_css)
        
        # 阴影
        if style.shadow:
            shadow_css = self._shadow_to_css(style.shadow)
            css_parts.append(shadow_css)
        
        # 自定义CSS
        if style.custom_css:
            for prop, value in style.custom_css.items():
                css_parts.append(f"{prop}: {value}")
        
        return "; ".join(css_parts)
    
    def _gradient_to_css(self, gradient: GradientConfig) -> str:
        """将渐变配置转换为CSS"""
        if gradient.type == "linear":
            colors = ", ".join(gradient.colors)
            return f"linear-gradient({gradient.direction}, {colors})"
        elif gradient.type == "radial":
            colors = ", ".join(gradient.colors)
            return f"radial-gradient({colors})"
        return ""
    
    def _font_to_css(self, font: FontConfig) -> List[str]:
        """将字体配置转换为CSS"""
        css_parts = []
        css_parts.append(f"font-family: {font.family}")
        css_parts.append(f"font-size: {font.size}")
        css_parts.append(f"font-weight: {font.weight}")
        css_parts.append(f"line-height: {font.line_height}")
        css_parts.append(f"color: {font.color}")
        
        if font.letter_spacing != "normal":
            css_parts.append(f"letter-spacing: {font.letter_spacing}")
        
        return css_parts
    
    def _spacing_to_css(self, spacing: SpacingConfig, prop_name: str) -> str:
        """将间距配置转换为CSS"""
        return f"{prop_name}: {spacing.top} {spacing.right} {spacing.bottom} {spacing.left}"
    
    def _border_to_css(self, border: BorderConfig) -> List[str]:
        """将边框配置转换为CSS"""
        css_parts = []
        css_parts.append(f"border: {border.width} {border.style} {border.color}")
        
        if border.radius != "0px":
            css_parts.append(f"border-radius: {border.radius}")
        
        return css_parts
    
    def _shadow_to_css(self, shadow: ShadowConfig) -> str:
        """将阴影配置转换为CSS"""
        return f"box-shadow: {shadow.x} {shadow.y} {shadow.blur} {shadow.spread} {shadow.color}"

# 异步包装函数
async def generate_pdf_from_json_config(config_dict: Dict[str, Any], output_path: Optional[str] = None) -> str:
    """从JSON配置字典生成PDF"""
    config = CoverLetterConfig(**config_dict)
    generator = JSONToPDFGenerator()
    return await generator.generate_pdf_from_json(config, output_path)

def generate_pdf_sync(config_dict: Dict[str, Any], output_path: Optional[str] = None) -> str:
    """同步版本的PDF生成函数"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(generate_pdf_from_json_config(config_dict, output_path))
    finally:
        loop.close()