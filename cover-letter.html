
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>求职信 - Talent Acquisition Specialist, China (Remote) @ BJAK </title>
        <style>
            @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&family=Kalam:wght@400;700&family=Dancing+Script:wght@400;500;600;700&display=swap');

            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                line-height: 1.7;
                color: #1a202c;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
                min-height: 100vh;
                padding: 30px 20px;
                position: relative;
            }

            body::before {
                content: '';
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
                z-index: -1;
            }

            .container {
                max-width: 900px;
                margin: 0 auto;
                background: rgba(255, 255, 255, 0.98);
                backdrop-filter: blur(20px);
                border-radius: 24px;
                box-shadow:
                    0 32px 64px rgba(0, 0, 0, 0.12),
                    0 0 0 1px rgba(255, 255, 255, 0.05);
                overflow: hidden;
                position: relative;
                border: 4px solid transparent;
                background-clip: padding-box;
            }

            .container::before {
                content: '';
                position: absolute;
                top: -4px;
                left: -4px;
                right: -4px;
                bottom: -4px;
                background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
                border-radius: 28px;
                z-index: -1;
            }

            @keyframes gradientShift {
                0%, 100% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
            }

            .header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 45px 40px;
                text-align: center;
                position: relative;
                overflow: hidden;
                min-height: 180px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
            }

            .header::before {
                content: '';
                position: absolute;
                top: -50%;
                left: -50%;
                width: 200%;
                height: 200%;
                background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
                background-size: 30px 30px;
                animation: float 20s linear infinite;
            }

            @keyframes float {
                0% { transform: translate(-50%, -50%) rotate(0deg); }
                100% { transform: translate(-50%, -50%) rotate(360deg); }
            }

            .company-logo {
                width: clamp(60px, 8vw, 80px);
                height: clamp(60px, 8vw, 80px);
                min-width: 50px;
                min-height: 50px;
                border-radius: 16px;
                margin: 0 auto 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                position: relative;
                z-index: 2;
                transition: all 0.3s ease;
                background: rgba(255, 255, 255, 0.15);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
                overflow: hidden;
            }

            .company-logo:hover {
                transform: translateY(-3px) scale(1.05);
                background: rgba(255, 255, 255, 0.25);
                border-color: rgba(255, 255, 255, 0.4);
                box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
            }

            .company-logo img {
                width: auto;
                height: auto;
                max-width: calc(100% - 10px);
                max-height: calc(100% - 10px);
                min-width: 30px;
                min-height: 30px;
                object-fit: contain;
                border-radius: 6px;
                filter: brightness(1.1) contrast(1.1) saturate(1.05);
                transition: filter 0.3s ease;
            }
            
            .company-logo img:hover {
                filter: brightness(1.15) contrast(1.15) saturate(1.1);
            }
            
            /* 针对不同比例logo的特殊处理 */
            .company-logo.wide-logo {
                width: clamp(80px, 12vw, 120px);
                height: clamp(50px, 6vw, 70px);
            }
            
            .company-logo.tall-logo {
                width: clamp(50px, 6vw, 70px);
                height: clamp(80px, 12vw, 120px);
            }
            
            /* Logo占位符样式 */
            .logo-placeholder {
                width: 100%;
                height: 100%;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 24px;
                font-weight: bold;
                color: white;
                text-transform: uppercase;
                letter-spacing: 2px;
                border-radius: 12px;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
                transition: all 0.3s ease;
            }
            
            .logo-placeholder:hover {
                transform: scale(1.05);
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
            }
            
            /* 响应式设计 */
            @media (max-width: 768px) {
                .company-logo {
                    width: clamp(50px, 10vw, 65px);
                    height: clamp(50px, 10vw, 65px);
                }
                
                .company-logo.wide-logo {
                    width: clamp(65px, 15vw, 90px);
                    height: clamp(40px, 8vw, 55px);
                }
                
                .company-logo.tall-logo {
                    width: clamp(40px, 8vw, 55px);
                    height: clamp(65px, 15vw, 90px);
                }
            }
            
            @media (max-width: 480px) {
                .company-logo {
                    width: 45px;
                    height: 45px;
                }
                
                .company-logo img {
                    min-width: 25px;
                    min-height: 25px;
                }
            }

            .header-title {
                font-family: 'Inter', 'Microsoft YaHei', serif;
                font-size: 32px;
                font-weight: 700;
                margin-bottom: 15px;
                position: relative;
                z-index: 2;
                text-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                color: #ffffff;
                letter-spacing: 0.5px;
                line-height: 1.2;
            }

            .header-subtitle {
                font-size: 16px;
                opacity: 0.98;
                position: relative;
                z-index: 2;
                font-weight: 500;
                letter-spacing: 0.3px;
                color: rgba(255, 255, 255, 0.95);
                line-height: 1.4;
                font-family: 'Inter', 'Microsoft YaHei', sans-serif;
            }

            .header-subtitle a {
                color: #ffffff !important;
                text-decoration: none;
                font-weight: 600;
                border-bottom: 1px solid rgba(255, 255, 255, 0.3);
                padding-bottom: 1px;
                transition: all 0.3s ease;
            }

            .header-subtitle a:hover {
                border-bottom-color: rgba(255, 255, 255, 0.8);
                text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
            }

            .header-subtitle .separator {
                margin: 0 8px;
                opacity: 0.7;
                font-weight: 300;
            }

            .content {
                padding: 40px 50px 30px 50px;
                background: white;
            }

            .greeting {
                font-size: 20px;
                font-weight: 600;
                margin-bottom: 35px;
                color: #2d3748;
                position: relative;
                padding-left: 0px;
                text-align: left !important;
                display: block;
                width: 100%;
            }

            .greeting::before {
                content: '';
                position: absolute;
                left: 0;
                top: 0;
                font-size: 18px;
            }

            .main-content {
                font-size: 17px;
                line-height: 1.8;
                margin-bottom: 45px;
                color: #4a5568;
                text-align: justify;
            }

            .chinese-content {
                margin-bottom: 30px;
                font-family: 'Inter', 'Microsoft YaHei', sans-serif;
                text-align: left;
            }

            .english-content {
                margin-top: 30px;
                font-family: 'Inter', sans-serif;
                font-style: italic;
                text-align: left;
            }

            .separator {
                margin: 30px 0;
                text-align: center;
                width: 100%;
            }

            .separator hr {
                border: none;
                height: 2px;
                background: linear-gradient(90deg, #667eea, #764ba2);
                margin: 0 auto;
                width: 100%;
                border-radius: 1px;
            }

            .highlight {
                background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
                padding: 3px 8px;
                border-radius: 6px;
                font-weight: 600;
                color: #2d3748;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            }

            .stats-section {
                background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
                border-radius: 20px;
                padding: 25px;
                margin: 25px 0 15px 0;
                border: 1px solid #e2e8f0;
                position: relative;
                overflow: hidden;
            }

            .stats-section::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 4px;
                background: linear-gradient(90deg, #667eea, #764ba2);
            }

            .stats-title {
                font-size: 22px;
                font-weight: 700;
                margin-bottom: 15px;
                color: #2d3748;
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .stats-grid {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
                margin-top: 15px;
            }

            .stat-card {
                background: white;
                padding: 15px 12px;
                border-radius: 16px;
                text-align: center;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
                border: none;
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
                min-width: 0;
                flex: 1;
            }

            .stat-card::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 3px;
                background: linear-gradient(90deg, #667eea, #764ba2);
                transform: scaleX(0);
                transition: transform 0.3s ease;
            }

            .stat-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
            }

            .stat-card:hover::before {
                transform: scaleX(1);
            }

            .stat-value {
                font-size: 32px;
                font-weight: 800;
                background: linear-gradient(135deg, #667eea, #764ba2);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                margin-bottom: 8px;
                line-height: 1.2;
                position: relative;
            }

            .stat-value::after {
                content: '';
                position: absolute;
                bottom: -10px;
                left: 50%;
                transform: translateX(-50%);
                width: 40px;
                height: 3px;
                background: linear-gradient(90deg, #667eea, #764ba2);
                border-radius: 2px;
                opacity: 0.6;
            }

            .stat-label {
                font-size: 15px;
                color: #718096;
                font-weight: 600;
                letter-spacing: 0.5px;
            }

            /* 圆形进度条样式 */
            .progress-circle {
                width: 80px;
                height: 80px;
                margin: 0 auto 15px;
                position: relative;
            }

            .progress-circle svg {
                width: 100%;
                height: 100%;
                transform: rotate(-90deg);
            }

            .progress-circle .progress-bg {
                fill: none;
                stroke: #e2e8f0;
                stroke-width: 8;
            }

            .progress-circle .progress-bar {
                fill: none;
                stroke: url(#gradient);
                stroke-width: 8;
                stroke-linecap: round;
                stroke-dasharray: 251.2;
                stroke-dashoffset: 251.2;
                animation: progressAnimation 2s ease-out forwards;
            }

            @keyframes progressAnimation {
                to {
                    stroke-dashoffset: calc(251.2 - (251.2 * var(--progress)) / 100);
                }
            }

            .progress-text {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                font-size: 18px;
                font-weight: 700;
                color: #2d3748;
            }

            /* 技能雷达图样式 */
            .radar-chart {
                width: 100%;
                height: 200px;
                margin: 20px 0;
                position: relative;
                background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .radar-chart::before {
                content: '';
                position: absolute;
                width: 80%;
                height: 80%;
                border: 2px solid rgba(102, 126, 234, 0.2);
                border-radius: 50%;
            }

            .radar-chart::after {
                content: '';
                position: absolute;
                width: 60%;
                height: 60%;
                border: 2px solid rgba(102, 126, 234, 0.3);
                border-radius: 50%;
            }

            .radar-point {
                position: absolute;
                width: 8px;
                height: 8px;
                background: linear-gradient(135deg, #667eea, #764ba2);
                border-radius: 50%;
                box-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
                animation: pulse 2s infinite;
            }

            @keyframes pulse {
                0%, 100% {
                    transform: scale(1);
                    opacity: 1;
                }
                50% {
                    transform: scale(1.2);
                    opacity: 0.8;
                }
            }

            /* 匹配度条形图 */
            .match-bar {
                width: 100%;
                height: 12px;
                background: #e2e8f0;
                border-radius: 6px;
                overflow: hidden;
                margin: 10px 0;
                position: relative;
            }

            .match-fill {
                height: 100%;
                background: linear-gradient(90deg, #667eea, #764ba2);
                border-radius: 6px;
                width: 0%;
                animation: fillAnimation 2s ease-out forwards;
                position: relative;
            }

            .match-fill::after {
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                width: 20px;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3));
                animation: shimmer 2s infinite;
            }

            @keyframes fillAnimation {
                to {
                    width: var(--match-percentage);
                }
            }

            @keyframes shimmer {
                0% {
                    transform: translateX(-20px);
                }
                100% {
                    transform: translateX(20px);
                }
            }

            .closing {
                font-size: 18px;
                margin-top: 30px;
                color: #2d3748;
                line-height: 1.6;
                font-family: 'Inter', 'Microsoft YaHei', sans-serif;
                font-weight: 400;
            }

            .signature {
                margin-top: 50px;
                text-align: right;
                position: relative;
                font-family: 'Inter', 'Microsoft YaHei', sans-serif;
            }

            .signature-name {
                font-family: 'Kalam', 'Dancing Script', cursive;
                font-size: 28px;
                font-weight: 700;
                color: #2d3748;
                margin-bottom: 8px;
                transform: rotate(-2deg);
                display: inline-block;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
            }

            .signature-date {
                font-size: 19px;
                color: #2d3748;
                font-weight: 500;
                font-family: 'Inter', 'Microsoft YaHei', sans-serif;
            }

            .footer {
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                padding: 30px 50px;
                text-align: center;
                font-size: 15px;
                color: #718096;
                border-top: 1px solid #e2e8f0;
                position: relative;
            }

            .footer::before {
                content: '';
                position: absolute;
                top: 0;
                left: 50%;
                transform: translateX(-50%);
                width: 60px;
                height: 3px;
                background: linear-gradient(90deg, #667eea, #764ba2);
                border-radius: 0 0 3px 3px;
            }

            @media (max-width: 768px) {
                body {
                    padding: 20px 15px;
                }

                .container {
                    border-radius: 20px;
                }

                .header {
                    padding: 40px 30px;
                }

                .content {
                    padding: 40px 30px;
                }

                .footer {
                    padding: 25px 30px;
                }

                .stats-section {
                    padding: 20px;
                    margin: 20px 0;
                }

                .stats-grid {
                    grid-template-columns: repeat(2, 1fr);
                    gap: 12px;
                }

                .stat-card {
                    padding: 12px 8px;
                }

                .stat-value {
                    font-size: 28px;
                }

                .stat-label {
                    font-size: 12px;
                }

                .header-title {
                    font-size: 28px;
                }

                .company-logo {
                    width: 70px;
                    height: 70px;
                }
            }

            /* 小屏幕优化 - 保持三列布局 */
            @media (max-width: 600px) {
                .stats-section {
                    padding: 15px;
                    margin: 15px 0;
                }

                .stats-grid {
                    grid-template-columns: repeat(2, 1fr);
                    gap: 8px;
                }

                .stat-card {
                    padding: 10px 6px;
                }

                .stat-value {
                    font-size: 24px;
                }

                .stat-label {
                    font-size: 11px;
                }
            }

            /* PDF打印优化样式 - 全面升级版 */
            @media print {
                @page {
                    size: A4;
                    margin: 0.6in 0.4in;
                    -webkit-print-color-adjust: exact;
                    print-color-adjust: exact;
                    color-adjust: exact;
                    orphans: 3;
                    widows: 3;
                }

                * {
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    color-adjust: exact !important;
                }

                body {
                    background: #f8fafc !important;
                    padding: 0 !important;
                    margin: 0 !important;
                    font-size: 13px !important;
                    line-height: 1.7 !important;
                    font-family: 'Inter', 'Segoe UI', 'Microsoft YaHei', Arial, sans-serif !important;
                    color: #1a202c !important;
                    -webkit-font-smoothing: antialiased !important;
                    -moz-osx-font-smoothing: grayscale !important;
                    font-smoothing: antialiased !important;
                    text-rendering: optimizeLegibility !important;
                    font-feature-settings: "liga" 1, "kern" 1 !important;
                    font-variant-ligatures: common-ligatures !important;
                }

                /* 图像和Logo优化 */
                img, .company-logo {
                    image-rendering: -webkit-optimize-contrast !important;
                    image-rendering: crisp-edges !important;
                    -ms-interpolation-mode: bicubic !important;
                    max-width: 100% !important;
                    height: auto !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    color-adjust: exact !important;
                }

                .container {
                    margin: 0 auto !important;
                    padding: 0 !important;
                    background: #fff !important;
                    max-width: 900px !important;
                    width: 100% !important;
                    border-radius: 12px !important;
                    border: 3px solid transparent !important;
                    background-clip: padding-box !important;
                    position: relative !important;
                }

                .container::before {
                    content: '' !important;
                    position: absolute !important;
                    top: -3px !important;
                    left: -3px !important;
                    right: -3px !important;
                    bottom: -3px !important;
                    background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #667eea) !important;
                    background-size: 400% 400% !important;
                    border-radius: 15px !important;
                    z-index: -1 !important;
                    animation: gradientShift 8s ease-in-out infinite !important;
                }

                .header {
                    padding: 24px 0 18px 0 !important;
                    margin-bottom: 12px !important;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
                    border-bottom: 2px solid #2d3748 !important;
                    color: #fff !important;
                    page-break-after: avoid !important;
                }

                .header-title {
                    font-size: 24px !important;
                    font-weight: 700 !important;
                    color: white !important;
                    text-shadow: 1px 1px 2px rgba(0,0,0,0.3) !important;
                    letter-spacing: 0.5px !important;
                }

                .header-subtitle {
                    font-size: 14px !important;
                    color: rgba(255,255,255,0.9) !important;
                    margin-top: 5px !important;
                    font-weight: 500 !important;
                }

                .company-logo {
                    width: 50px !important;
                    height: 50px !important;
                    border: 2px solid white !important;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.2) !important;
                }

                .content {
                    padding: 15px 0 !important;
                    margin: 0 !important;
                    font-size: 11px !important;
                    line-height: 1.6 !important;
                }

                .stats-section {
                    padding: 18px !important;
                    margin: 20px 0 !important;
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
                    border: 2px solid #cbd5e0 !important;
                    border-radius: 10px !important;
                    page-break-inside: avoid;
                    break-inside: avoid;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    position: relative !important;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.08) !important;
                }

                .stats-section::before {
                    content: '' !important;
                    position: absolute !important;
                    top: 0 !important;
                    left: 0 !important;
                    width: 100% !important;
                    height: 5px !important;
                    background: linear-gradient(90deg, #4c51bf, #553c9a) !important;
                    border-radius: 10px 10px 0 0 !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    color-adjust: exact !important;
                }

                .stats-title {
                    font-size: 18px !important;
                    margin-bottom: 12px !important;
                    line-height: 1.3 !important;
                    color: #1a202c !important;
                    font-weight: 700 !important;
                    text-align: center !important;
                    letter-spacing: 0.3px !important;
                }

                .stats-grid {
                    gap: 12px !important;
                    margin-top: 12px !important;
                    display: grid !important;
                    grid-template-columns: repeat(2, 1fr) !important;
                }

                .stat-card {
                    padding: 12px 8px !important;
                    background: white !important;
                    border: 2px solid #e2e8f0 !important;
                    border-radius: 8px !important;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
                    text-align: center !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    display: block !important;
                    position: relative !important;
                    transition: none !important;
                }

                .stat-card::before {
                    content: '' !important;
                    position: absolute !important;
                    top: 0 !important;
                    left: 0 !important;
                    width: 100% !important;
                    height: 3px !important;
                    background: linear-gradient(90deg, #4c51bf, #553c9a) !important;
                    border-radius: 8px 8px 0 0 !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    color-adjust: exact !important;
                }

                .stat-card::after {
                    display: none !important;
                }

                .stat-value {
                    font-size: 22px !important;
                    margin-bottom: 6px !important;
                    color: #4c51bf !important;
                    line-height: 1.2 !important;
                    font-weight: 800 !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    font-family: 'Inter', sans-serif !important;
                }

                .stat-label {
                    font-size: 10px !important;
                    color: #4a5568 !important;
                    line-height: 1.3 !important;
                    margin-top: 4px !important;
                    font-weight: 600 !important;
                    letter-spacing: 0.2px !important;
                    text-transform: uppercase !important;
                }

                .footer {
                    padding: 10px 0 !important;
                    margin-top: 15px !important;
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
                    border-top: 2px solid #e2e8f0 !important;
                    text-align: center !important;
                    font-size: 10px !important;
                    color: #718096 !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    color-adjust: exact !important;
                }

                .footer::before {
                    content: '' !important;
                    position: absolute !important;
                    top: 0 !important;
                    left: 50% !important;
                    transform: translateX(-50%) !important;
                    width: 40px !important;
                    height: 2px !important;
                    background: linear-gradient(90deg, #667eea, #764ba2) !important;
                    border-radius: 0 0 2px 2px !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    color-adjust: exact !important;
                }

                .signature {
                    margin-top: 20px !important;
                    text-align: right !important;
                    position: relative !important;
                }

                .signature-name {
                    font-size: 18px !important;
                    font-weight: 700 !important;
                    color: #2d3748 !important;
                    margin-bottom: 4px !important;
                }

                .signature-date {
                    font-size: 14px !important;
                    color: #2d3748 !important;
                    font-weight: 500 !important;
                }

                .closing {
                    font-size: 14px !important;
                    margin-top: 20px !important;
                    color: #2d3748 !important;
                    line-height: 1.4 !important;
                    font-weight: 500 !important;
                }

                /* 显示匹配度条形图 */
                .match-bar {
                    width: 100% !important;
                    height: 8px !important;
                    background: #e2e8f0 !important;
                    border-radius: 4px !important;
                    overflow: hidden !important;
                    margin: 6px 0 !important;
                    position: relative !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    color-adjust: exact !important;
                }

                .match-fill {
                    height: 100% !important;
                    background: linear-gradient(90deg, #667eea, #764ba2) !important;
                    border-radius: 4px !important;
                    width: var(--match-percentage, 85%) !important;
                    position: relative !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    color-adjust: exact !important;
                }

                /* 隐藏其他装饰性元素 */
                .progress-circle,
                .radar-chart {
                    display: none !important;
                }

                /* 优化文本段落间距 */
                p {
                    margin: 3px 0 !important;
                    line-height: 1.3 !important;
                }

                /* 确保内容不被分页截断 */
                .stats-section,
                .content > div {
                    page-break-inside: avoid;
                    break-inside: avoid;
                }

                /* 优化标题间距 */
                h1, h2, h3, h4, h5, h6 {
                    margin: 4px 0 2px 0 !important;
                    line-height: 1.2 !important;
                }

                /* 移除大部分动画和过渡效果，但保留渐变边框动画 */
                *:not(.container::before) {
                    animation: none !important;
                    transition: none !important;
                    transform: none !important;
                }

                /* 浏览器打印特殊优化 */
                html {
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    color-adjust: exact !important;
                }

                /* 确保网格布局在打印时稳定 */
                .stats-grid {
                    display: grid !important;
                    grid-template-columns: repeat(2, 1fr) !important;
                    width: 100% !important;
                    box-sizing: border-box !important;
                }

                /* 强制显示所有边框和背景 */
                .stat-card, .stats-section {
                    border-style: solid !important;
                    background-clip: padding-box !important;
                    -webkit-background-clip: padding-box !important;
                }

                /* 文本渲染优化 */
                .stat-value, .stat-label, .stats-title {
                    text-rendering: optimizeLegibility !important;
                    -webkit-font-smoothing: antialiased !important;
                    -moz-osx-font-smoothing: grayscale !important;
                }

                /* 渐变边框动画定义 */
                @keyframes gradientShift {
                    0%, 100% { background-position: 0% 50% !important; }
                    50% { background-position: 100% 50% !important; }
                }
            }

            /* 超小屏幕优化 - 仍保持三列 */
            @media (max-width: 480px) {
                .stats-section {
                    padding: 12px;
                    margin: 12px 0;
                }

                .stats-grid {
                    grid-template-columns: repeat(2, 1fr);
                    gap: 6px;
                }

                .stat-card {
                    padding: 8px 4px;
                }

                .stat-value {
                    font-size: 22px;
                }

                .stat-label {
                    font-size: 10px;
                }
            }
        </style>
    </head>
    <body>
        <!-- SVG定义，用于渐变和图形 -->
        <svg width="0" height="0" style="position: absolute;">
            <defs>
                <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
                </linearGradient>
                <filter id="glow">
                    <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                    <feMerge>
                        <feMergeNode in="coloredBlur"/>
                        <feMergeNode in="SourceGraphic"/>
                    </feMerge>
                </filter>
            </defs>
        </svg>

        <div class="container">
            <div class="header">
                <div class="company-logo" id="companyLogo">
                    <img id="logoImg" src="https://logo.clearbit.com/bjak.com" alt="BJAK  Logo" 
                         style="display: none;" onload="handleLogoLoad(this)" 
                         onerror="handleLogoError(this)">
                    <div class="logo-placeholder" id="logoPlaceholder">
                        B
                    </div>
                </div>
                <div class="header-title">To: BJAK </div>
                <div class="header-subtitle">
                    <a href="https://www.linkedin.com/jobs/view/4253646517/" target="_blank">Talent Acquisition Specialist, China (Remote)</a><span class="separator">•</span>职位申请<span class="separator">•</span> China
                </div>
            </div>

            <div class="content">
                
        <div class="greeting">
            尊敬的 BJAK  招聘团队，您好！
        </div>

        <div class="main-content">
            
        <div class="chinese-content">
            <p>我对 BJAK 的创新精神及其在东盟金融科技领域的领先地位深感认同，尤其对中国区远程人才招聘专家职位充满热情。</p>
<p>我在施罗德基金管理公司（中国）的业务发展实习经验，培养了严谨的数据分析、策略制定和信息维护能力，这与理解招聘需求、分析人才市场及高效管理 ATS 记录高度契合。同时，我在小爱宠物用品公司的市场营销专长，包括深入的市场调研和引人入胜的内容策划，可直接应用于构建多元化人才管道、强化雇主品牌建设并确保卓越的候选人体验。我擅长跨部门协作与沟通，并拥有举办大型活动的组织能力，能有效支持端到端招聘流程。凭借我的全球化视野、分析能力和流利的中文，我期待能为 BJAK 在中国市场的人才战略贡献力量。</p>
        </div>
        
            <div class="separator">
                <hr style="margin: 30px 0; border: none; height: 2px; background: linear-gradient(90deg, #667eea, #764ba2);">
            </div>
            <div class="english-content">
                <p>I am deeply drawn to BJAK's innovative spirit and its leadership in Southeast Asian FinTech, and I am particularly enthusiastic about the Talent Acquisition Specialist, China (Remote) position.</p>
<p>While my background is not traditionally in Human Resources, my diverse experience has cultivated a highly transferable skill set directly applicable to talent acquisition. My Business Development Internship at Schroders Fund Management (China) honed my abilities in meticulous data analysis, strategic target setting, and robust information maintenance, aligning perfectly with understanding staffing needs, analyzing talent markets, and efficiently managing ATS records. Simultaneously, as a Marketing Specialist at Little Darling Pet Supplies, I gained extensive experience in in-depth market research and developing compelling content, which directly translates to building diverse talent pipelines, strengthening employer branding initiatives, and ensuring a positive candidate experience. My proven capacity for cross-functional collaboration, impactful communication, and organizing large-scale events will enable me to effectively manage the end-to-end recruitment process. With my analytical mindset, global perspective, and native Mandarin proficiency, I am confident in my ability to significantly contribute to BJAK's talent strategy in the Chinese market.</p>
            </div>
            
        </div>

        <div class="closing">
            感谢您的时间和考虑！期待与您进一步交流的机会。
        </div>

        <div class="signature">
            <div class="signature-name">Zhengyi GAN</div>
            <div class="signature-date">2025年06月24日</div>
        </div>
        

                <div class="stats-section">
                    <div class="stats-title">📊 匹配度分析</div>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-value">88%</div>
                            <div class="stat-label">技能匹配度</div>
                            <div class="match-bar">
                                <div class="match-fill" data-percentage="88" style="--match-percentage: 88%;"></div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">92%</div>
                            <div class="stat-label">经验相关性</div>
                            <div class="match-bar">
                                <div class="match-fill" data-percentage="92" style="--match-percentage: 92%;"></div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">95%</div>
                            <div class="stat-label">学习潜力</div>
                            <div class="match-bar">
                                <div class="match-fill" data-percentage="95" style="--match-percentage: 95%;"></div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">90%</div>
                            <div class="stat-label">文化契合度</div>
                            <div class="match-bar">
                                <div class="match-fill" data-percentage="90" style="--match-percentage: 90%;"></div>
                            </div>
                        </div>
                    </div>
                </div>


            </div>


        </div>

        <script>
            // Logo处理相关变量
            const logoUrls = ['https://logo.clearbit.com/bjak.com', 'https://img.logo.dev/bjak.com?token=pk_X-1ZO13GSgeOoUrIuJ6GMQ', 'https://logo.brandfetch.io/bjak.com', 'https://api.logo.dev/bjak.com?size=400&format=png', 'https://logo.uplead.com/bjak.com', 'https://favicons.githubusercontent.com/bjak.com', 'https://www.google.com/s2/favicons?domain=bjak.com&sz=128', 'https://logo.safedk.com/bjak.com/logo.png', 'https://besticon-demo.herokuapp.com/icon?url=bjak.com&size=128', 'https://logo.clearbit.com/bjak.com', 'https://external-content.duckduckgo.com/iu/?u=https%3A//logo.clearbit.com/bjak.com', 'https://ui-avatars.com/api/?name=BJ&background=667eea&color=fff&size=128&font-size=0.6&rounded=true', 'https://via.placeholder.com/128x128/667eea/ffffff?text=BJ', 'https://logo.clearbit.com/bjak.co', 'https://img.logo.dev/bjak.co?token=pk_X-1ZO13GSgeOoUrIuJ6GMQ', 'https://logo.brandfetch.io/bjak.co', 'https://api.logo.dev/bjak.co?size=400&format=png', 'https://logo.uplead.com/bjak.co', 'https://favicons.githubusercontent.com/bjak.co', 'https://www.google.com/s2/favicons?domain=bjak.co&sz=128'];
            let currentLogoIndex = 0;
            const companyName = 'BJAK ';
            let logoLoadAttempts = 0;
            const maxLogoAttempts = 3;
            
            // Logo质量检测函数
            function checkLogoQuality(img) {
                const minSize = 32;
                const maxSize = 512;
                const width = img.naturalWidth;
                const height = img.naturalHeight;
                const aspectRatio = width / height;
                
                // 基本尺寸检查
                if (width < minSize || height < minSize) return false;
                if (width > maxSize && height > maxSize) return false;
                
                // 宽高比检查（避免过于极端的比例）
                if (aspectRatio > 3 || aspectRatio < 0.33) return false;
                
                // 检查是否为有效图片（避免1x1像素的占位图）
                if (width === 1 && height === 1) return false;
                
                return true;
            }
            
            // Logo加载成功处理
            function handleLogoLoad(img) {
                console.log('Logo加载成功:', img.src);
                
                // 质量检测
                if (!checkLogoQuality(img)) {
                    console.log('Logo质量不符合要求，尝试下一个');
                    handleLogoError(img);
                    return;
                }
                
                img.style.display = 'block';
                document.getElementById('logoPlaceholder').style.display = 'none';
                
                // 检测logo比例并应用相应样式
                const logoContainer = document.getElementById('companyLogo');
                const aspectRatio = img.naturalWidth / img.naturalHeight;
                
                if (aspectRatio > 1.5) {
                    logoContainer.classList.add('wide-logo');
                    logoContainer.classList.remove('tall-logo');
                } else if (aspectRatio < 0.7) {
                    logoContainer.classList.add('tall-logo');
                    logoContainer.classList.remove('wide-logo');
                } else {
                    logoContainer.classList.remove('wide-logo', 'tall-logo');
                }
                
                // 添加加载成功的视觉反馈
                logoContainer.style.opacity = '0';
                setTimeout(() => {
                    logoContainer.style.transition = 'opacity 0.3s ease';
                    logoContainer.style.opacity = '1';
                }, 50);
                
                // 缓存成功的logo（发送到后端）
                cacheLogo(companyName, img.src, img.src);
            }
            
            // Logo加载失败处理
            function handleLogoError(img) {
                console.log('Logo加载失败:', img.src);
                logoLoadAttempts++;
                
                // 如果当前URL还有重试机会
                if (logoLoadAttempts < maxLogoAttempts && currentLogoIndex < logoUrls.length) {
                    console.log(`重试当前logo (${logoLoadAttempts}/${maxLogoAttempts}):`, img.src);
                    setTimeout(() => {
                        img.src = img.src + '?retry=' + logoLoadAttempts;
                    }, 1000 * logoLoadAttempts); // 递增延迟
                    return;
                }
                
                // 重置重试计数，尝试下一个URL
                logoLoadAttempts = 0;
                currentLogoIndex++;
                
                if (currentLogoIndex < logoUrls.length) {
                    // 尝试下一个logo URL
                    console.log('尝试下一个logo:', logoUrls[currentLogoIndex]);
                    img.src = logoUrls[currentLogoIndex];
                } else {
                    // 所有logo都失败，显示占位符
                    console.log('所有logo都加载失败，显示占位符');
                    showLogoPlaceholder();
                }
            }
            
            // 显示logo占位符
            function showLogoPlaceholder() {
                const img = document.getElementById('logoImg');
                const placeholder = document.getElementById('logoPlaceholder');
                const logoContainer = document.getElementById('companyLogo');
                
                img.style.display = 'none';
                placeholder.style.display = 'flex';
                
                // 生成公司首字母或使用默认图标
                const firstLetter = companyName.charAt(0).toUpperCase() || 'C';
                placeholder.textContent = firstLetter;
                
                // 添加渐入动画
                placeholder.style.opacity = '0';
                setTimeout(() => {
                    placeholder.style.transition = 'opacity 0.3s ease';
                    placeholder.style.opacity = '1';
                }, 50);
                
                // 移除可能的logo样式类
                logoContainer.classList.remove('wide-logo', 'tall-logo');
            }
            
            // 缓存logo到后端
            function cacheLogo(companyName, logoUrl, logoData) {
                // 防止重复缓存
                if (window.logoCacheAttempted) return;
                window.logoCacheAttempted = true;
                
                try {
                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时
                    
                    fetch('/cache-logo', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            company_name: companyName,
                            logo_url: logoUrl,
                            logo_data: logoData
                        }),
                        signal: controller.signal
                    }).then(response => {
                        clearTimeout(timeoutId);
                        if (response.ok) {
                            console.log('Logo缓存成功');
                            return response.json();
                        } else {
                            console.log('Logo缓存失败，状态码:', response.status);
                        }
                    }).catch(error => {
                        clearTimeout(timeoutId);
                        if (error.name === 'AbortError') {
                            console.log('Logo缓存超时');
                        } else {
                            console.log('Logo缓存失败:', error.message);
                        }
                    });
                } catch (error) {
                    console.log('Logo缓存请求失败:', error.message);
                }
            }
            
            // 预加载下一个logo（性能优化）
            function preloadNextLogo() {
                if (currentLogoIndex + 1 < logoUrls.length) {
                    const nextImg = new Image();
                    nextImg.src = logoUrls[currentLogoIndex + 1];
                    console.log('预加载下一个logo:', nextImg.src);
                }
            }
            
            // 页面加载完成后启动动画和logo优化
            document.addEventListener('DOMContentLoaded', function() {
                // 启动logo预加载
                preloadNextLogo();
                
                // 添加logo加载超时处理
                const logoImg = document.getElementById('logoImg');
                const logoTimeout = setTimeout(() => {
                    if (logoImg.style.display === 'none' || !logoImg.complete) {
                        console.log('Logo加载超时，显示占位符');
                        showLogoPlaceholder();
                    }
                }, 8000); // 8秒超时
                
                // 如果logo成功加载，清除超时
                logoImg.addEventListener('load', () => {
                    clearTimeout(logoTimeout);
                });
                
                // 为每个统计卡片添加动画
                const statCards = document.querySelectorAll('.stat-card');
                statCards.forEach((card, index) => {
                    setTimeout(() => {
                        card.style.opacity = '0';
                        card.style.transform = 'translateY(20px)';
                        card.style.transition = 'all 0.6s ease';

                        setTimeout(() => {
                            card.style.opacity = '1';
                            card.style.transform = 'translateY(0)';
                        }, 100);
                    }, index * 200);
                });

                // 为匹配度条形图添加动画
                const matchBars = document.querySelectorAll('.match-fill');
                matchBars.forEach((bar, index) => {
                    setTimeout(() => {
                        const percentage = bar.getAttribute('data-percentage') || '85';
                        bar.style.setProperty('--match-percentage', percentage + '%');
                    }, 1000 + index * 300);
                });

                // 为圆形进度条添加动画
                const progressBars = document.querySelectorAll('.progress-bar');
                progressBars.forEach((bar, index) => {
                    setTimeout(() => {
                        const percentage = bar.getAttribute('data-percentage') || '85';
                        bar.style.setProperty('--progress', percentage);
                    }, 1500 + index * 200);
                });
            });
        </script>
    </body>
    </html>
    